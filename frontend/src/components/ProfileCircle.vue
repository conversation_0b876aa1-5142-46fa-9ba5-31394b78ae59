<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import echo from '@/services/echo'

const authStore = useAuthStore()
const router = useRouter()
const showDropdown = ref(false)
const showUserInfo = ref(false)

// Connection status
const isConnected = ref(false)
const isAuthenticated = ref(false)
const socketId = ref('')

// Update connection status
const updateConnectionStatus = () => {
  try {
    isConnected.value = echo.connector.pusher.connection.state === 'connected'
    socketId.value = echo.socketId() || ''
    isAuthenticated.value = !!socketId.value
  } catch (error) {
    console.error('Error checking connection status:', error)
    isConnected.value = false
    isAuthenticated.value = false
  }
}

onMounted(() => {
  // Initial check
  updateConnectionStatus()

  // Set up event listeners
  if (echo.connector.pusher) {
    echo.connector.pusher.connection.bind('connected', () => {
      isConnected.value = true
      socketId.value = echo.socketId() || ''
      isAuthenticated.value = !!socketId.value
    })

    echo.connector.pusher.connection.bind('disconnected', () => {
      isConnected.value = false
      isAuthenticated.value = false
    })

    echo.connector.pusher.connection.bind('error', () => {
      isConnected.value = false
      isAuthenticated.value = false
    })
  }

  // Periodic check (every 5 seconds)
  const interval = setInterval(updateConnectionStatus, 5000)

  onUnmounted(() => {
    clearInterval(interval)
    if (echo.connector.pusher) {
      echo.connector.pusher.connection.unbind('connected')
      echo.connector.pusher.connection.unbind('disconnected')
      echo.connector.pusher.connection.unbind('error')
    }
  })
})

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

const closeDropdown = () => {
  showDropdown.value = false
}

const goToProfile = () => {
  router.push('/profile')
  closeDropdown()
}



const logout = async () => {
  await authStore.logout()
  router.push('/login')
  closeDropdown()
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.profile-circle-container')) {
    closeDropdown()
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="profile-circle-container">
    <div
      class="profile-section"
      @click="toggleDropdown"
      @mouseenter="showUserInfo = true"
      @mouseleave="showUserInfo = false"
    >
      <span class="username">{{ authStore.user?.username }}</span>
      <div class="profile-circle">
        <!-- Profile Image or Placeholder -->
        <div class="profile-image">
          <img
            v-if="authStore.user?.profile_image_url"
            :src="authStore.user.profile_image_url"
            :alt="authStore.user.username"
            class="profile-img"
          />
          <div v-else class="profile-placeholder">
            {{ authStore.user?.username?.charAt(0).toUpperCase() }}
          </div>
        </div>

        <!-- Online Status Indicator -->
        <div
          class="status-dot"
          :class="{
            'online': isConnected && isAuthenticated,
            'offline': !isConnected || !isAuthenticated
          }"
        ></div>
      </div>
    </div>

    <!-- User Info Tooltip -->
    <div v-if="showUserInfo" class="user-info-tooltip">
      <div class="username">{{ authStore.user?.username }}</div>
      <div class="user-id">ID: {{ socketId ? socketId.substring(0, 6) + '...' : 'N/A' }}</div>
      <div class="status">{{ isConnected ? 'Online' : 'Offline' }}</div>
    </div>

    <!-- Dropdown Menu -->
    <div v-if="showDropdown" class="dropdown-menu">
      <div class="dropdown-header">
        <div class="dropdown-username">{{ authStore.user?.username }}</div>
        <div class="dropdown-status">
          <span class="status-indicator" :class="{ 'online': isConnected, 'offline': !isConnected }"></span>
          {{ isConnected ? 'Online' : 'Offline' }}
        </div>
      </div>
      <div class="dropdown-divider"></div>
      <button @click="goToProfile" class="dropdown-item">
        <span class="dropdown-icon">⚙️</span>
        Profile Settings
      </button>
      <div class="dropdown-divider"></div>
      <button @click="logout" class="dropdown-item logout">
        <span class="dropdown-icon">🚪</span>
        Logout
      </button>
    </div>
  </div>
</template>

<style scoped>
.profile-circle-container {
  position: relative;
  display: flex;
  align-items: center;
}

.profile-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
}

.profile-section:hover {
  background-color: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.username {
  font-weight: 500;
  color: #495057;
  font-size: 0.9rem;
}

.profile-circle {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  flex-shrink: 0;
}

.profile-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.profile-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-placeholder {
  font-size: 1.2rem;
  font-weight: bold;
  color: #666;
}

.status-dot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.status-dot.online {
  background-color: #4CAF50;
}

.status-dot.offline {
  background-color: #F44336;
}

.user-info-tooltip {
  position: absolute;
  top: 50px;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}

.user-info-tooltip::before {
  content: '';
  position: absolute;
  top: -5px;
  right: 10px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid rgba(0, 0, 0, 0.8);
}

.username {
  font-weight: bold;
  margin-bottom: 0.2rem;
}

.user-id, .status {
  font-size: 0.7rem;
  opacity: 0.8;
}

.dropdown-menu {
  position: absolute;
  top: 50px;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  z-index: 1000;
  overflow: hidden;
}

.dropdown-header {
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.dropdown-username {
  font-weight: bold;
  color: #333;
  margin-bottom: 0.25rem;
}

.dropdown-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #666;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.online {
  background-color: #4CAF50;
}

.status-indicator.offline {
  background-color: #F44336;
}

.dropdown-divider {
  height: 1px;
  background-color: #eee;
  margin: 0.5rem 0;
}

.dropdown-item {
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #333;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item.logout {
  color: #dc3545;
}

.dropdown-item.logout:hover {
  background-color: #f8d7da;
}

.dropdown-icon {
  font-size: 1rem;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .user-info-tooltip {
    display: none;
  }

  .dropdown-menu {
    right: -10px;
  }

  .profile-section {
    padding: 0.25rem 0.5rem;
  }

  .username {
    display: none;
  }
}
</style>
