<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import echo from '@/services/echo'
import { useAuthStore } from '@/stores/auth'

import EmojiAutocomplete from './EmojiAutocomplete.vue'
import { useEmojiAutocomplete } from '@/composables/useEmojiAutocomplete'
import type { Message, MessageHistory } from '@/types/message'
import { formatTimestamp, convertApiMessageToMessage, convertBroadcastMessageToMessage } from '@/types/message'
import axios from 'axios'

const messages = ref<Message[]>([])
const newMessage = ref('')
const authStore = useAuthStore()
const messagesContainer = ref<HTMLElement>()
const messageInput = ref<HTMLInputElement>()

const isLoadingHistory = ref(false)

// Emoji functionality
const {
  isVisible: isAutocompleteVisible,
  selectedIndex: autocompleteSelectedIndex,
  filteredEmojis,
  showAutocomplete,
  hideAutocomplete,
  selectNext,
  selectPrevious,
  getSelectedEmoji
} = useEmojiAutocomplete()

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

const loadChatHistory = async () => {
  if (isLoadingHistory.value) return

  try {
    isLoadingHistory.value = true
    const response = await axios.get<MessageHistory>(`${import.meta.env.VITE_API_URL}/api/messages/history?per_page=50`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })

    // Convert API messages to internal format and reverse to show oldest first
    const historyMessages = response.data.data
      .map(msg => convertApiMessageToMessage(msg, authStore.user?.id || 0))
      .reverse()

    messages.value = historyMessages
    scrollToBottom()
  } catch (error) {
    console.error('Failed to load chat history:', error)
  } finally {
    isLoadingHistory.value = false
  }
}

const sendMessage = async () => {
  if (!newMessage.value.trim()) return

  const messageText = newMessage.value

  try {
    // Clear input immediately
    newMessage.value = ''
    hideAutocomplete()

    // Send to server
    await axios.post(`${import.meta.env.VITE_API_URL}/api/messages`, {
      message: messageText
    }, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    })

    // The message will be added via the real-time broadcast
  } catch (error) {
    console.error('Failed to send message:', error)
    // Restore message on error
    newMessage.value = messageText
  }
}



// Autocomplete functions
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value
  const cursorPosition = target.selectionStart || 0

  // Look for emoji shortcode pattern (:word)
  const beforeCursor = value.substring(0, cursorPosition)
  const match = beforeCursor.match(/:([a-zA-Z_]+)$/)

  if (match) {
    const query = match[1]
    showAutocomplete(query)
  } else {
    hideAutocomplete()
  }
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (isAutocompleteVisible.value && filteredEmojis.value.length > 0) {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        selectNext()
        break
      case 'ArrowUp':
        event.preventDefault()
        selectPrevious()
        break
      case 'Tab':
      case 'Enter':
        if (event.key === 'Enter' && !event.shiftKey) {
          event.preventDefault()
          const selectedEmoji = getSelectedEmoji()
          if (selectedEmoji) {
            insertSelectedEmoji(selectedEmoji.emoji)
          } else {
            sendMessage()
          }
        } else if (event.key === 'Tab') {
          event.preventDefault()
          const selectedEmoji = getSelectedEmoji()
          if (selectedEmoji) {
            insertSelectedEmoji(selectedEmoji.emoji)
          }
        }
        break
      case 'Escape':
        event.preventDefault()
        hideAutocomplete()
        break
    }
  } else if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const insertSelectedEmoji = (emoji: string) => {
  if (!messageInput.value) return

  const cursorPosition = messageInput.value.selectionStart || 0
  const value = newMessage.value
  const beforeCursor = value.substring(0, cursorPosition)
  const afterCursor = value.substring(cursorPosition)

  // Find the start of the emoji shortcode
  const match = beforeCursor.match(/:([a-zA-Z_]+)$/)
  if (match) {
    const startPos = cursorPosition - match[0].length
    newMessage.value = value.substring(0, startPos) + emoji + afterCursor

    // Set cursor position after the emoji
    nextTick(() => {
      if (messageInput.value) {
        const newCursorPos = startPos + emoji.length
        messageInput.value.setSelectionRange(newCursorPos, newCursorPos)
        messageInput.value.focus()
      }
    })
  }

  hideAutocomplete()
}

const onAutocompleteEmojiSelected = (emoji: any) => {
  insertSelectedEmoji(emoji.emoji)
}

onMounted(async () => {
  console.log('ChatBox: Setting up Echo listener on chat channel');

  // Load chat history first
  await loadChatHistory()

  const channel = echo.channel('chat');

  // Listen for all events on this channel for debugging
  channel.listen('*', (eventName: string, data: any) => {
    console.log('ChatBox: Received any event:', eventName, data);
  });

  channel.listen('.MessageSent', (e: { message: string, username: string, userId: number, timestamp: number }) => {
    console.log('ChatBox: Received .MessageSent event:', e);

    // Add all messages (including own) since we're not adding them locally anymore
    const newMsg = convertBroadcastMessageToMessage(e, authStore.user?.id || 0)
    messages.value.push(newMsg);
    scrollToBottom()
  });

  console.log('ChatBox: Echo listener setup complete');
})

onUnmounted(() => {
  console.log('ChatBox: Cleaning up Echo listener');
  // Clean up listeners
  echo.leave('chat');
  console.log('ChatBox: Echo listener cleanup complete');
})
</script>

<template>
  <div class="chat-box">
    <h2>Real-time Chat</h2>

    <div class="messages-container" ref="messagesContainer">
      <div v-if="isLoadingHistory" class="loading-messages">
        Loading chat history...
      </div>
      <div v-else-if="messages.length === 0" class="no-messages">
        No messages yet. Start the conversation!
      </div>
      <div v-else class="messages">
        <div
          v-for="(msg, index) in messages"
          :key="msg.id || index"
          class="message"
          :class="{ 'self': msg.isSelf }"
        >
          <div class="message-header" v-if="!msg.isSelf">
            <span class="username">{{ msg.username }}</span>
            <span class="timestamp" v-if="msg.timestamp">{{ formatTimestamp(msg.timestamp) }}</span>
          </div>
          <div class="message-content">
            {{ msg.text }}
          </div>
          <div class="message-timestamp" v-if="msg.isSelf && msg.timestamp">
            {{ formatTimestamp(msg.timestamp) }}
          </div>
        </div>
      </div>
    </div>

    <div class="message-input-container">
      <EmojiAutocomplete
        :emojis="filteredEmojis"
        :selectedIndex="autocompleteSelectedIndex"
        :isVisible="isAutocompleteVisible"
        @emoji-selected="onAutocompleteEmojiSelected"
      />

      <div class="message-input">
        <input
          ref="messageInput"
          v-model="newMessage"
          @input="handleInput"
          @keydown="handleKeyDown"
          placeholder="Type a message... (use :emoji: for emojis)"
          type="text"
        />
        <button @click="sendMessage" :disabled="!newMessage.trim()">Send</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-box {
  width: 100%;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

h2 {
  padding: 1rem;
  margin: 0;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eee;
}

.messages-container {
  height: 300px;
  overflow-y: auto;
  padding: 1rem;
}

.loading-messages {
  text-align: center;
  color: #999;
  padding: 2rem;
  font-style: italic;
  font-size: 0.9rem;
}

.no-messages {
  color: #999;
  text-align: center;
  padding: 2rem;
}

.messages {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.message {
  max-width: 80%;
  align-self: flex-start;
}

.message.self {
  align-self: flex-end;
}

.message-header {
  margin-bottom: 0.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.username {
  font-size: 0.75rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timestamp {
  font-size: 0.7rem;
  color: #999;
  font-weight: normal;
  text-transform: none;
  letter-spacing: normal;
}

.message-content {
  padding: 0.75rem 1rem;
  border-radius: 18px;
  background-color: #f1f1f1;
  word-wrap: break-word;
}

.message.self .message-content {
  background-color: #42b883;
  color: white;
}

.message-timestamp {
  font-size: 0.7rem;
  color: #999;
  text-align: right;
  margin-top: 0.25rem;
}

.message-input-container {
  position: relative;
  background-color: white;
  border-top: 1px solid #eee;
}

.message-input {
  display: flex;
  padding: 1rem;
  gap: 0.5rem;
}

.message-input input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 20px;
  outline: none;
}

.message-input input:focus {
  border-color: #42b883;
}



.message-input button {
  padding: 0.75rem 1.5rem;
  background-color: #42b883;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.message-input button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.message-input button:hover:not(:disabled) {
  background-color: #3aa876;
}
</style>
