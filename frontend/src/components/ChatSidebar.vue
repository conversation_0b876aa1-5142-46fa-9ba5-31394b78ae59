<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useChatStore } from '@/stores/chat'
import { useAuthStore } from '@/stores/auth'
import EmojiAutocomplete from './EmojiAutocomplete.vue'
import { useEmojiAutocomplete } from '@/composables/useEmojiAutocomplete'
import { formatTimestamp } from '@/types/message'

// Define emits for parent communication
const emit = defineEmits(['close'])

const chatStore = useChatStore()
const authStore = useAuthStore()
const newMessage = ref('')
const messagesContainer = ref<HTMLElement>()
const messageInput = ref<HTMLInputElement>()

// Emoji functionality
const {
  isVisible: isAutocompleteVisible,
  selectedIndex: autocompleteSelectedIndex,
  filteredEmojis,
  showAutocomplete,
  hideAutocomplete,
  selectNext,
  selectPrevious,
  getSelectedEmoji
} = useEmojiAutocomplete()

const closeChat = () => {
  emit('close')
}

const isAtBottom = ref(true)
const lastMessageCount = ref(0)

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

const checkIfAtBottom = () => {
  if (!messagesContainer.value) return false
  const { scrollTop, scrollHeight, clientHeight } = messagesContainer.value
  // Consider "at bottom" if within 50px of the bottom
  return scrollHeight - scrollTop - clientHeight < 50
}

// Watch for new messages and handle scrolling intelligently
watch(() => chatStore.messages.length, (newLength, oldLength) => {
  const messageCountIncreased = newLength > oldLength
  const isLoadingMore = chatStore.isLoadingMore

  if (messageCountIncreased) {
    if (isLoadingMore) {
      // Loading older messages - preserve scroll position
      // Don't scroll to bottom
      return
    } else {
      // New message arrived - only scroll if user was at bottom
      if (isAtBottom.value) {
        scrollToBottom()
      }
    }
  }

  lastMessageCount.value = newLength
})

const sendMessage = async () => {
  if (!newMessage.value.trim()) return

  const messageText = newMessage.value
  newMessage.value = ''
  hideAutocomplete()

  try {
    await chatStore.sendMessage(messageText)
  } catch (error) {
    console.error('Failed to send message:', error)
    // Restore message on error
    newMessage.value = messageText
  }
}

// Handle scroll for lazy loading and tracking bottom position
const handleScroll = () => {
  if (!messagesContainer.value) return

  const { scrollTop } = messagesContainer.value

  // Update whether user is at bottom
  isAtBottom.value = checkIfAtBottom()

  // If scrolled near the top and there are more messages, load them
  if (scrollTop < 100 && chatStore.hasMoreMessages && !chatStore.isLoadingMore) {
    // Store current scroll position before loading more messages
    const previousScrollHeight = messagesContainer.value.scrollHeight

    chatStore.loadMoreMessages().then(() => {
      // After loading more messages, maintain relative scroll position
      nextTick(() => {
        if (messagesContainer.value) {
          const newScrollHeight = messagesContainer.value.scrollHeight
          const heightDifference = newScrollHeight - previousScrollHeight
          messagesContainer.value.scrollTop = scrollTop + heightDifference
        }
      })
    })
  }
}

const onEmojiSelected = (emoji: string) => {
  newMessage.value += emoji
  if (messageInput.value) {
    messageInput.value.focus()
  }
}

// Autocomplete functions
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = target.value
  const cursorPosition = target.selectionStart || 0

  // Look for emoji shortcode pattern (:word)
  const beforeCursor = value.substring(0, cursorPosition)
  const match = beforeCursor.match(/:([a-zA-Z_]+)$/)

  if (match) {
    const query = match[1]
    showAutocomplete(query)
  } else {
    hideAutocomplete()
  }
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (isAutocompleteVisible.value && filteredEmojis.value.length > 0) {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        selectNext()
        break
      case 'ArrowUp':
        event.preventDefault()
        selectPrevious()
        break
      case 'Tab':
      case 'Enter':
        if (event.key === 'Enter' && !event.shiftKey) {
          event.preventDefault()
          const selectedEmoji = getSelectedEmoji()
          if (selectedEmoji) {
            insertSelectedEmoji(selectedEmoji.emoji)
          } else {
            sendMessage()
          }
        } else if (event.key === 'Tab') {
          event.preventDefault()
          const selectedEmoji = getSelectedEmoji()
          if (selectedEmoji) {
            insertSelectedEmoji(selectedEmoji.emoji)
          }
        }
        break
      case 'Escape':
        event.preventDefault()
        hideAutocomplete()
        break
    }
  } else if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

const insertSelectedEmoji = (emoji: string) => {
  if (!messageInput.value) return

  const cursorPosition = messageInput.value.selectionStart || 0
  const value = newMessage.value
  const beforeCursor = value.substring(0, cursorPosition)
  const afterCursor = value.substring(cursorPosition)

  // Find the start of the emoji shortcode
  const match = beforeCursor.match(/:([a-zA-Z_]+)$/)
  if (match) {
    const startPos = cursorPosition - match[0].length
    newMessage.value = value.substring(0, startPos) + emoji + afterCursor

    // Set cursor position after the emoji
    nextTick(() => {
      if (messageInput.value) {
        const newCursorPos = startPos + emoji.length
        messageInput.value.setSelectionRange(newCursorPos, newCursorPos)
        messageInput.value.focus()
      }
    })
  }

  hideAutocomplete()
}

const onAutocompleteEmojiSelected = (emoji: any) => {
  insertSelectedEmoji(emoji.emoji)
}



onMounted(async () => {
  // Initialize chat store
  await chatStore.initialize()

  // Add scroll listener for lazy loading
  if (messagesContainer.value) {
    messagesContainer.value.addEventListener('scroll', handleScroll)
    // Initialize scroll position tracking
    isAtBottom.value = checkIfAtBottom()
  }

  // Scroll to bottom on initial load
  scrollToBottom()
  lastMessageCount.value = chatStore.messages.length
})

onUnmounted(() => {
  // Remove scroll listener
  if (messagesContainer.value) {
    messagesContainer.value.removeEventListener('scroll', handleScroll)
  }
})
</script>

<template>
  <div class="chat-sidebar">
    <div class="sidebar-header">
      <h3>Chat</h3>
      <button @click="closeChat" class="close-btn mobile-only">×</button>
    </div>

    <div class="sidebar-content">
      <div class="messages-container" ref="messagesContainer">
        <div v-if="chatStore.isLoadingMore" class="loading-messages">
          Loading more messages...
        </div>
        <div v-if="chatStore.isLoadingHistory" class="loading-messages">
          Loading chat history...
        </div>
        <div v-else-if="chatStore.messages.length === 0" class="no-messages">
          No messages yet. Start the conversation!
        </div>
        <div v-else class="messages">
          <div
            v-for="(msg, index) in chatStore.messages"
            :key="msg.id || msg.tempId || index"
            class="message"
            :class="{ 'self': msg.isSelf }"
          >
            <div class="message-header">
              <div class="message-avatar">
                <img
                  v-if="msg.profileImageUrl"
                  :src="msg.profileImageUrl"
                  :alt="msg.username"
                  class="profile-image"
                />
                <div v-else class="profile-placeholder">
                  {{ msg.username.charAt(0).toUpperCase() }}
                </div>
              </div>
              <div class="message-info">
                <span class="username">{{ msg.username }}</span>
                <span class="timestamp">
                  <span v-if="msg.status === 'sending'">sending...</span>
                  <span v-else-if="msg.status === 'failed'" class="failed">failed</span>
                  <span v-else-if="msg.timestamp">{{ formatTimestamp(msg.timestamp) }}</span>
                </span>
              </div>
            </div>
            <div class="message-content">
              {{ msg.text }}
            </div>
          </div>
        </div>
      </div>

      <div class="message-input-container">
        <EmojiAutocomplete
          :emojis="filteredEmojis"
          :selectedIndex="autocompleteSelectedIndex"
          :isVisible="isAutocompleteVisible"
          @emoji-selected="onAutocompleteEmojiSelected"
        />

        <div class="message-input">
          <input
            ref="messageInput"
            v-model="newMessage"
            @input="handleInput"
            @keydown="handleKeyDown"
            placeholder="Type a message... (use :emoji: for emojis)"
            type="text"
          />
          <button @click="sendMessage" :disabled="!newMessage.trim()">Send</button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.chat-sidebar {
  position: fixed;
  top: var(--header-height, 80px);
  left: 0;
  width: 320px;
  height: calc(100vh - var(--header-height, 80px));
  background-color: white;
  border-right: 1px solid #e0e0e0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 100;
  transition: width 0.3s ease;
}



.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  min-height: 60px;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  color: #666;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background-color: #e9ecef;
}

.mobile-only {
  display: none;
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background-color: #fafafa;
}

.no-messages {
  color: #999;
  text-align: center;
  padding: 2rem 1rem;
  font-size: 0.9rem;
}

.messages {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.message {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0.5rem;
  border-radius: 6px;
  background-color: transparent;
}

.message.self {
  background-color: rgba(66, 184, 131, 0.1);
}

.message-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.message-avatar {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
}

.profile-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.profile-placeholder {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #42b883;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.7rem;
}

.message-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
}

.username {
  font-size: 0.75rem;
  font-weight: 600;
  color: #333;
}

.timestamp {
  font-size: 0.65rem;
  color: #999;
}

.timestamp .failed {
  color: #dc3545;
}

.loading-messages {
  text-align: center;
  color: #999;
  padding: 2rem 1rem;
  font-style: italic;
  font-size: 0.9rem;
}

.message-content {
  padding: 0.25rem 0;
  color: #333;
  font-size: 0.85rem;
  line-height: 1.3;
  word-wrap: break-word;
  margin-left: 34px; /* Align with message text after avatar */
}

.message-input-container {
  position: relative;
  background-color: white;
  border-top: 1px solid #e0e0e0;
}

.message-input {
  display: flex;
  padding: 1rem;
  gap: 0.5rem;
}

.message-input input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 0.9rem;
  outline: none;
}

.message-input input:focus {
  border-color: #42b883;
}



.message-input button {
  padding: 0.5rem 1rem;
  background-color: #42b883;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.message-input button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.message-input button:hover:not(:disabled) {
  background-color: #3aa876;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .chat-sidebar {
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1000;
    border-right: none;
    box-shadow: none;
  }

  .mobile-only {
    display: flex;
  }
}
</style>
