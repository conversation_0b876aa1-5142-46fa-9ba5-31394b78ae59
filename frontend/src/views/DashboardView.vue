<script setup lang="ts">
import { onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useRouter } from 'vue-router';

const authStore = useAuthStore();
const router = useRouter();

onMounted(() => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
  }
});

const logout = async () => {
  await authStore.logout();
  router.push('/login');
};
</script>

<template>
  <div class="dashboard">
    <h1>Dashboard</h1>
    <div v-if="authStore.user" class="user-info">
      <p>Welcome, {{ authStore.user.username }}!</p>
      <button @click="logout" class="logout-btn">Logout</button>
    </div>

    <div class="dashboard-content">
      <p>This is your dashboard. The chat is now available in the sidebar on the right!</p>
      <p>You can navigate to any page and the chat will remain accessible.</p>
    </div>
  </div>
</template>

<style scoped>
.dashboard {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.user-info {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.logout-btn {
  padding: 0.5rem 1rem;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
}

.dashboard-content {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #42b883;
}

.dashboard-content p {
  margin-bottom: 1rem;
  color: #666;
}

.dashboard-content p:last-child {
  margin-bottom: 0;
}
</style>
