<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import { onMounted, ref, watch } from 'vue'
import { useAuthStore } from './stores/auth'
import { useChatStore } from './stores/chat'
import ProfileCircle from './components/ProfileCircle.vue'
import ChatSidebar from './components/ChatSidebar.vue'

const authStore = useAuthStore()
const chatStore = useChatStore()
const isMobileMenuOpen = ref(false)



const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

const toggleChat = () => {
  chatStore.toggleVisibility()
}

// Fetch user data on app mount if token exists
onMounted(async () => {
  if (authStore.token && !authStore.user) {
    await authStore.fetchUser()
  }

  // Initialize chat visibility from localStorage
  if (authStore.isAuthenticated) {
    chatStore.initializeVisibility()
  }
})

// Watch for authentication changes to reset chat
watch(() => authStore.isAuthenticated, (isAuth) => {
  if (!isAuth) {
    chatStore.reset()
  } else {
    chatStore.initializeVisibility()
  }
})
</script>

<template>
  <div class="app-container" :class="{ 'with-sidebar': authStore.isAuthenticated && chatStore.isVisible, 'sidebar-collapsed': authStore.isAuthenticated && !chatStore.isVisible }" ref="appContainer">
    <header class="header">
      <div class="header-content">
        <div class="header-left">
          <!-- Chat Toggle Button -->
          <button
            v-if="authStore.isAuthenticated"
            @click="toggleChat"
            class="chat-toggle-btn"
            :class="{ 'active': chatStore.isVisible }"
          >
            💬
          </button>

          <div class="logo">
            <h1>tbc</h1>
          </div>
        </div>

        <div class="header-right">
          <!-- Desktop Navigation for authenticated users -->
          <nav v-if="authStore.isAuthenticated" class="nav desktop-nav">
            <RouterLink to="/dashboard" class="nav-link" @click="closeMobileMenu">Dashboard</RouterLink>
          </nav>

          <!-- Profile Section for authenticated users -->
          <div v-if="authStore.isAuthenticated" class="profile-section-wrapper">
            <ProfileCircle />
          </div>

          <!-- Desktop Navigation for non-authenticated users -->
          <nav v-else class="nav desktop-nav">
            <RouterLink to="/login" class="nav-link" @click="closeMobileMenu">Login</RouterLink>
            <RouterLink to="/register" class="nav-link" @click="closeMobileMenu">Register</RouterLink>
          </nav>

          <!-- Mobile Hamburger Button -->
          <button class="mobile-menu-btn" @click="toggleMobileMenu" :class="{ 'active': isMobileMenuOpen }">
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <nav class="mobile-nav" :class="{ 'open': isMobileMenuOpen }">
        <template v-if="authStore.isAuthenticated">
          <RouterLink to="/dashboard" class="mobile-nav-link" @click="closeMobileMenu">Dashboard</RouterLink>
          <RouterLink to="/profile" class="mobile-nav-link" @click="closeMobileMenu">Profile</RouterLink>
        </template>
        <template v-else>
          <RouterLink to="/login" class="mobile-nav-link" @click="closeMobileMenu">Login</RouterLink>
          <RouterLink to="/register" class="mobile-nav-link" @click="closeMobileMenu">Register</RouterLink>
        </template>
      </nav>
    </header>

    <main class="content">
      <div class="content-inner">
        <RouterView />
      </div>
    </main>

    <!-- Chat Sidebar - keep loaded when authenticated but hide when not visible -->
    <ChatSidebar
      v-if="authStore.isAuthenticated"
      :class="{ 'chat-hidden': !chatStore.isVisible }"
      @close="toggleChat"
    />
  </div>
</template>

<style>
/* Reset CSS */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --header-height: 80px;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  font-family: Arial, sans-serif;
  background-color: #f8f8f8;
  color: #333;
}

#app {
  width: 100%;
  min-height: 100%;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.header {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  height: var(--header-height);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 200;
  transition: padding-left 0.3s ease;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 0 0 auto; /* Don't grow or shrink */
}

.chat-toggle-btn {
  background: #42b883;
  border: none;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chat-toggle-btn:hover {
  background: #3aa876;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.chat-toggle-btn.active {
  background: #2d7a5f;
}

.logo {
  display: flex;
  align-items: center;
}

.logo h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #42b883;
  letter-spacing: 1px;
}

.nav {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
}

.nav-link:hover {
  color: #42b883;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: #42b883;
  transition: width 0.3s;
}

.nav-link:hover::after,
.router-link-active::after {
  width: 100%;
}

.router-link-active {
  color: #42b883;
}

.content {
  flex: 1;
  width: 100%;
  background-color: white;
  margin-top: var(--header-height);
  transition: margin-left 0.3s ease;
  min-height: calc(100vh - var(--header-height));
}

.app-container.with-sidebar .content {
  margin-left: 320px;
  /* Center content in the remaining space */
  display: flex;
  justify-content: center;
}

.app-container.with-sidebar .content-inner {
  /* Remove default width and let content size naturally */
  width: auto;
  max-width: 800px; /* Match the typical page max-width */
}

.app-container.with-sidebar.sidebar-collapsed .content {
  margin-left: 60px;
}

/* Removed header padding that was causing chat button and title to shift when chat opens */

.content-inner {
  width: 100%;
  padding: 2rem;
  box-sizing: border-box;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 0 0 auto; /* Don't grow or shrink */
}

.profile-section-wrapper {
  display: flex;
  align-items: center;
}



/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.mobile-menu-btn span {
  width: 100%;
  height: 3px;
  background-color: #333;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.mobile-menu-btn.active span:nth-child(1) {
  transform: rotate(45deg) translate(8px, 8px);
}

.mobile-menu-btn.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-btn.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  flex-direction: column;
  background-color: white;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.mobile-nav.open {
  max-height: 300px;
}

.mobile-nav-link {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  padding: 1rem 2rem;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.mobile-nav-link:hover,
.mobile-nav-link.router-link-active {
  background-color: #f8f9fa;
  color: #42b883;
}

@media (max-width: 768px) {
  .header-content {
    padding: 1rem 2rem;
  }

  .desktop-nav {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .mobile-nav {
    display: flex;
  }

  .content-inner {
    padding: 1rem;
  }

  .app-container.with-sidebar .content {
    margin-left: 0;
    display: block; /* Reset flex on mobile */
    justify-content: initial;
  }

  .app-container.with-sidebar .content-inner {
    width: 100%; /* Reset width on mobile */
    max-width: none; /* Reset max-width on mobile */
  }

  /* Header padding rules removed - no longer needed */
}

/* Chat visibility */
.chat-hidden {
  display: none !important;
}
</style>
