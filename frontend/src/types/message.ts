export interface Message {
  id?: number
  text: string
  username: string
  userId: number
  isSelf: boolean
  timestamp?: number // Unix timestamp
  created_at_unix?: number // For API responses
  status?: 'sending' | 'sent' | 'failed' // For optimistic updates
  tempId?: string // Temporary ID for optimistic updates
  profileImageUrl?: string // User's profile image URL
  isNewlyLoaded?: boolean // For animation when lazy loading
}

export interface MessageHistory {
  data: Array<{
    id: number
    content: string
    created_at_unix: number
    user: {
      id: number
      username: string
      profile_image_url?: string
    }
  }>
  current_page: number
  last_page: number
  per_page: number
  total: number
}

export interface MessageApiResponse {
  status: string
  message: {
    id: number
    content: string
    created_at_unix: number
    user: {
      id: number
      username: string
      profile_image_url?: string
    }
  }
}

// Utility function to format timestamp to local time
export function formatTimestamp(timestamp: number): string {
  const date = new Date(timestamp * 1000) // Convert from Unix timestamp
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

  const timeString = date.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  })

  if (messageDate.getTime() === today.getTime()) {
    // Today - just show time
    return timeString
  } else if (messageDate.getTime() === today.getTime() - 24 * 60 * 60 * 1000) {
    // Yesterday
    return `Yesterday ${timeString}`
  } else if (date.getFullYear() === now.getFullYear()) {
    // This year - show month/day and time
    return date.toLocaleDateString([], {
      month: 'short',
      day: 'numeric'
    }) + ` ${timeString}`
  } else {
    // Different year - show full date and time
    return date.toLocaleDateString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }) + ` ${timeString}`
  }
}

// Convert API message to internal Message format
export function convertApiMessageToMessage(apiMessage: any, currentUserId: number): Message {
  return {
    id: apiMessage.id,
    text: apiMessage.content,
    username: apiMessage.user.username,
    userId: apiMessage.user.id,
    isSelf: apiMessage.user.id === currentUserId,
    timestamp: apiMessage.created_at_unix,
    profileImageUrl: apiMessage.user.profile_image_url
  }
}

// Convert real-time broadcast message to internal Message format
export function convertBroadcastMessageToMessage(broadcastData: any, currentUserId: number): Message {
  return {
    text: broadcastData.message,
    username: broadcastData.username,
    userId: broadcastData.userId,
    isSelf: broadcastData.userId === currentUserId,
    timestamp: broadcastData.timestamp,
    profileImageUrl: broadcastData.profileImageUrl
  }
}
